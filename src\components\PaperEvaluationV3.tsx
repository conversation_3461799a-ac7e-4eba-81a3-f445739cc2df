'use client';

import React, { ReactNode, useRef, useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import InlineGlossaryText from '@/components/InlineGlossaryText';
import SparklesIconWithTooltip from '@/components/SparklesIconWithTooltip';
import Tooltip from '@/components/Tooltip';
import { titleToId } from '@/utils/titleToId';

// Corrected Consolidated OUTLINE Icons Import from @heroicons/react/24/outline
import {
  AcademicCapIcon,
  AdjustmentsHorizontalIcon,
  ArrowPathIcon,
  ArrowTrendingUpIcon,
  BeakerIcon, // For Multi-Level Explanations & Methodological Deep Dive
  BookOpenIcon,
  BuildingLibraryIcon,
  ChartBarIcon,
  ChatBubbleBottomCenterTextIcon, // For Methodological Deep Dive
  ChatBubbleLeftEllipsisIcon,
  ChatBubbleLeftRightIcon, // For Multi-Level Explanations
  ChatBubbleOvalLeftEllipsisIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CircleStackIcon,
  ClipboardDocumentIcon, // For IRBuildSnippetIcon alias if needed elsewhere, or direct use
  ClipboardDocumentListIcon, // For Methodological Deep Dive & Prerequisites
  CodeBracketIcon, // For Multi-Level Explanations
  CodeBracketSquareIcon, // For Implementation Readiness (Code Link License)
  CogIcon, // For Multi-Level Explanations
  CommandLineIcon, // For Implementation Readiness (Build Snippet)
  CpuChipIcon,
  CubeTransparentIcon, // For Methodological Deep Dive section card
  DocumentDuplicateIcon,
  DocumentTextIcon, // For Final Verdict (Concluding Remarks)
  ExclamationTriangleIcon, // For Potential Challenges
  InformationCircleIcon, // For all tooltips
  KeyIcon, // For Key Learnings section card
  LightBulbIcon, // For Brainstormed Audio Applications (SubLightBulbIcon if different style needed)
  LinkIcon, // For Implementation Readiness (Code Link License)
  ListBulletIcon, // For Methodological Deep Dive (Step-by-step guide)
  MicrophoneIcon, // For Brainstormed Audio Applications
  MusicalNoteIcon, // For Brainstormed Audio Applications
  PaintBrushIcon,
  PencilSquareIcon,
  PuzzlePieceIcon,
  QuestionMarkCircleIcon, // Added for outline use
  ScaleIcon,
  ServerStackIcon,
  ShieldCheckIcon, // For Potential Challenges (Mitigation Strategies)
  ShieldExclamationIcon, // For Final Verdict (Limitations)
  SpeakerWaveIcon,
  Square3Stack3DIcon,
  TableCellsIcon,
  TrophyIcon, // For Final Verdict (Overall Recommendation)
  UserGroupIcon,
  UsersIcon,
  VariableIcon,
  WrenchScrewdriverIcon // For Implementation Readiness (Environment Spec)
} from '@heroicons/react/24/outline';

import {
  LightBulbIcon as MainLightBulbIcon, // Solid version for section card
  QuestionMarkCircleIcon as MainQuestionMarkCircleIcon, // Solid version for section card
  SparklesIcon as MainSparklesIcon, // Solid version for section card
  CheckCircleIcon as MainCheckCircleIcon, // Solid version for section card (e.g. Final Verdict)
  ExclamationTriangleIcon as MainExclamationTriangleIcon, // Solid version for section card (e.g. Challenges)
  DocumentTextIcon as MainDocumentTextIcon, // Solid version for section card (e.g. Abstract)
  CubeTransparentIcon as MainCubeTransparentIcon, // Solid version for section card
  CommandLineIcon as MainCommandLineIcon, // Solid version for section card
  CodeBracketSquareIcon as MainCodeBracketSquareIcon, // Solid version for section card
  ChatBubbleLeftRightIcon as MainChatBubbleLeftRightIcon, // Solid version for section card
  AcademicCapIcon as MainAcademicCapIcon, // Solid version for section card
  KeyIcon as MainKeyIcon // Solid version for section card
  // Add other SOLID icons if needed, ensuring Main prefix
} from '@heroicons/react/24/solid';

// Helper to interpolate colors for gradient effect
const interpolateColor = (percentage: number, color1: [number, number, number], color2: [number, number, number]): string => {
  const r = Math.round(color1[0] + percentage * (color2[0] - color1[0]));
  const g = Math.round(color1[1] + percentage * (color2[1] - color1[1]));
  const b = Math.round(color1[2] + percentage * (color2[2] - color1[2]));
  return `rgb(${r}, ${g}, ${b})`;
};

const START_COLOR_RGB: [number, number, number] = [59, 130, 246]; // blue-500
const END_COLOR_RGB: [number, number, number] = [168, 85, 247];   // purple-600

const scoreMarkers = [20, 40, 60, 80]; // Positions for dotted lines

// Define the structure of the V3 evaluation data
interface EvaluationDataV3 {
  metadata: {
    title: string;
    authors: string;
    year: number;
    doi: string;
  };
  executive_summary_for_audio_dev: string;
  scores: {
    implementation_readiness: { [key: string]: number };
    verified_performance_impact: { [key: string]: number };
    problem_solving_novelty_insight: { [key: string]: number };
    audio_domain_translatability_impact: { [key: string]: number };
    total_weighted_score: number;
  };
  detailed_pillar_analysis?: {
    clarity_and_presentation?: PillarNarratives;
    methodology_and_execution?: PillarNarratives;
    innovation_and_originality?: PillarNarratives;
    relevance_and_impact?: PillarNarratives;
    implementation_readiness?: PillarNarratives;
    verified_performance_impact?: PillarNarratives;
    problem_solving_novelty_insight?: PillarNarratives;
    audio_domain_translatability_impact?: PillarNarratives;
    [key: string]: PillarNarratives | undefined;
  };
  multi_level_explanation: {
    level_1_musician_friend: string;
    level_2_juce_developer_no_ai_expert: string;
    level_3_music_tech_researcher: string;
    level_4_ai_specialist_condensed: string;
  };
  brainstormed_audio_applications: Array<{
    application_idea: string;
    brief_description: string;
  }>;
  key_learnings_for_audio_dev: string[];
  critical_assessment_and_limitations: string;
  juce_implementation_sketch: {
    description: string;
    code_sketch: string;
  };
  methodological_deep_dive_adaptation: Array<{
    methodName: string;
    simplifiedExplanationForAudioDev: string;
    prerequisites_for_audio_adaptation: string[];
    stepByStepAdaptationGuide_conceptual: string[];
    practicalAudioExample: {
      scenarioDescription: string;
      how_method_might_apply: string;
      expected_audio_outcome_if_successful: string;
    };
  }>;
  impact_on_my_research_and_development: {
    new_ideas_sparked: string;
    refinement_of_existing_ideas: string;
    potential_thesis_contribution_angle: string;
    questions_for_further_investigation: string[];
  };
  final_verdict_for_audio_dev: {
    is_worth_reading_thoroughly: string;
    primary_value_proposition: string;
    overall_suitability_score_for_my_needs: string;
    concluding_remarks: string;
  };
}

interface PillarNarratives {
  narrative_summary?: string;
  strengths?: string[];
  weaknesses?: string[];
  suggestions_for_improvement?: string[];
}

interface SectionCardProps {
  title?: string;
  leadingIcon?: React.ElementType;
  children: ReactNode;
  id?: string;
  style?: React.CSSProperties;
  className?: string;
  tooltipText?: string;
}

// Helper function to extract text content from React children
function extractTextFromChildren(children: React.ReactNode): string {
  if (typeof children === 'string') {
    return children;
  }
  if (typeof children === 'number') {
    return String(children);
  }
  if (Array.isArray(children)) {
    return children.map(extractTextFromChildren).join('');
  }
  if (React.isValidElement(children)) {
    return extractTextFromChildren((children.props as any).children);
  }
  return '';
}

// Helper component to render markdown content with tooltips
function MarkdownContent({ content }: { content: string }) {
  if (typeof content !== 'string') {
    console.error('MarkdownContent received non-string content:', content);
    return <p className="text-red-500">Error: Content is not a string.</p>;
  }

  return (
    <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 leading-relaxed">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          p: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <p className="mb-4" {...props}>
                <InlineGlossaryText text={textContent} />
              </p>
            );
          },
          strong: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <strong className="font-semibold text-gray-900 dark:text-gray-100" {...props}>
                <InlineGlossaryText text={textContent} />
              </strong>
            );
          },
          a: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <a className="text-sky-600 dark:text-sky-400 hover:underline" {...props}>
                <InlineGlossaryText text={textContent} />
              </a>
            );
          },
          li: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <li {...props}>
                <InlineGlossaryText text={textContent} />
              </li>
            );
          },
          h1: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h1 {...props}>
                <InlineGlossaryText text={textContent} />
              </h1>
            );
          },
          h2: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h2 {...props}>
                <InlineGlossaryText text={textContent} />
              </h2>
            );
          },
          h3: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h3 {...props}>
                <InlineGlossaryText text={textContent} />
              </h3>
            );
          },
          h4: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h4 {...props}>
                <InlineGlossaryText text={textContent} />
              </h4>
            );
          },
          h5: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h5 {...props}>
                <InlineGlossaryText text={textContent} />
              </h5>
            );
          },
          h6: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h6 {...props}>
                <InlineGlossaryText text={textContent} />
              </h6>
            );
          },
          // Handle code blocks without tooltip processing to avoid issues
          code: ({ node, children, ...props }) => (
            <code {...props}>{children}</code>
          ),
          pre: ({ node, children, ...props }) => (
            <pre {...props}>{children}</pre>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}

// New SectionCard component for individual sections with top gradient border
const SectionCard: React.FC<SectionCardProps> = ({ title, leadingIcon: LeadingIcon, children, id, style, className, tooltipText }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<SVGSVGElement>(null);
  const trailingIconRef = useRef<SVGSVGElement>(null);
  const [iconStyle, setIconStyle] = useState({});
  const [trailingIconStyle, setTrailingIconStyle] = useState({});

  useEffect(() => {
    if (cardRef.current) {
      const cardRect = cardRef.current.getBoundingClientRect();
      if (cardRect.width <= 0) return;

      // Calculate style for the leading icon
      if (LeadingIcon && iconRef.current) {
        const iconRect = iconRef.current.getBoundingClientRect();
        const iconCenterX = iconRect.left + iconRect.width / 2;
        const cardStartX = cardRect.left;
        const relativeX = iconCenterX - cardStartX;
        let factor = relativeX / cardRect.width;
        factor = Math.max(0, Math.min(1, factor));
        const color = interpolateColor(factor, START_COLOR_RGB, END_COLOR_RGB);
        setIconStyle({ color });
      }

      // Calculate style for the trailing SparklesIcon
      if (trailingIconRef.current) {
        const trailingIconRect = trailingIconRef.current.getBoundingClientRect();
        const trailingIconCenterX = trailingIconRect.left + trailingIconRect.width / 2;
        const cardStartX = cardRect.left;
        const relativeXTrailing = trailingIconCenterX - cardStartX;
        let factorTrailing = relativeXTrailing / cardRect.width;
        factorTrailing = Math.max(0, Math.min(1, factorTrailing));
        const trailingColor = interpolateColor(factorTrailing, START_COLOR_RGB, END_COLOR_RGB);
        setTrailingIconStyle({ color: trailingColor });
      }
    }
  }, [LeadingIcon, title, children]);

  return (
    <div
      ref={cardRef}
      id={id}
      className={`bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 mb-8 ${className || ''}`}
      style={style}
    >
      <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
      <div className="p-6">
        {title && (
          <div className="flex items-center mb-6">
            {LeadingIcon && <LeadingIcon ref={iconRef} className={`h-7 w-7 mr-3 shrink-0`} style={iconStyle} />}
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white flex-grow">
              <InlineGlossaryText text={title} />
            </h2>
            {tooltipText && (
              <Tooltip text={tooltipText}>
                <InformationCircleIcon ref={trailingIconRef} className="h-7 w-7 ml-2 shrink-0 text-[#a855f7] dark:text-[#a855f7]" />
              </Tooltip>
            )}
            <SparklesIconWithTooltip
              className="h-7 w-7 ml-2 shrink-0 text-[#a855f7] dark:text-[#a855f7]"
              style={trailingIconStyle}
            />
          </div>
        )}
        <div className="pl-10">{children}</div>
      </div>
    </div>
  );
};

// Helper to format score keys into readable titles
const formatScoreKey = (key: string): string => {
  if (key === 'total_weighted_score') return 'Total Weighted Score';
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Helper to format multi-level explanation keys into readable titles
const formatMultiLevelKey = (key: string): string => {
  switch (key) {
    case 'level_1_musician_friend':
      return 'For a Musician Friend (Level 1)';
    case 'level_2_juce_developer_no_ai_expert':
      return 'For a JUCE Developer (No AI Expertise) (Level 2)';
    case 'level_3_music_tech_researcher':
      return 'For a Music Technology Researcher (Level 3)';
    case 'level_4_ai_specialist_condensed':
      return 'For an AI Specialist (Condensed) (Level 4)';
    default:
      return formatScoreKey(key);
  }
};

const subCriteriaIconMap: Record<string, React.ElementType> = {
  // Implementation Readiness
  code_link_license: LinkIcon,
  build_snippet: ClipboardDocumentIcon,
  environment_spec: WrenchScrewdriverIcon,
  minimal_example: DocumentTextIcon,

  // Verified Performance Impact
  metric_table: TableCellsIcon,
  benchmarked_code_output: ChartBarIcon,
  stat_sig_repetition: ArrowPathIcon,

  // Problem Solving Novelty Insight
  conceptual_innovation: LightBulbIcon,
  problem_re_framing: AdjustmentsHorizontalIcon,
  clarity_of_explanation: ChatBubbleLeftRightIcon,
  potential_for_unforeseen_applications: LightBulbIcon,

  // Audio Domain Translatability Impact
  direct_audio_application: MusicalNoteIcon,
  conceptual_audio_analogy: PuzzlePieceIcon,
  juce_cpp_integration_pathway: CommandLineIcon,
  workflow_enhancement_potential: ArrowTrendingUpIcon,

  default: QuestionMarkCircleIcon, // Changed to outline QuestionMarkCircleIcon
};

// Gets icon based on the raw scoreName (JSON key)
const getSubCriterionIcon = (scoreName: string): React.ElementType => {
  return subCriteriaIconMap[scoreName] || subCriteriaIconMap.default;
};

// Icon map for main pillar titles in the Detailed Pillar Analysis section
const narrativePillarIconMap: Record<string, React.ElementType> = {
  clarity_and_presentation: ChatBubbleBottomCenterTextIcon, // Reusing an existing suitable icon
  methodology_and_execution: CogIcon, // Reusing SubCogIcon
  innovation_and_originality: MainLightBulbIcon,
  relevance_and_impact: ArrowTrendingUpIcon,
  implementation_readiness: MainCommandLineIcon,
  verified_performance_impact: ChartBarIcon,
  problem_solving_novelty_insight: PuzzlePieceIcon,
  audio_domain_translatability_impact: SpeakerWaveIcon,
  default: MainQuestionMarkCircleIcon, // Fallback icon for narrative pillars
};

// Gets icon for main narrative pillar titles
const getNarrativePillarIcon = (pillarKey: string): React.ElementType => {
  return narrativePillarIconMap[pillarKey.toLowerCase().replace(/\s+/g, '_')] || narrativePillarIconMap.default;
};

// Tooltip texts for each section
const sectionTooltips: Record<string, string> = {
  // Top-level sections
  'metadata': "Basic bibliographical information about the research paper, such as its title, authors, publication year, and official identifier (like a DOI or arXiv ID).",
  'executive_summary_for_audio_dev': "A quick, engaging overview for audio plugin developers. It explains what the paper is about and highlights why it might be relevant or interesting for creating audio software with JUCE/C++.",
  'executive_summary_for_audio_developers': "A quick, engaging overview for audio plugin developers. It explains what the paper is about and highlights why it might be relevant or interesting for creating audio software with JUCE/C++.",
  'scores': "Quantitative evaluation of the paper across key criteria important for audio plugin development. Each criterion is scored, and a total weighted score indicates its overall potential.",
  'quantitative_scores': "Quantitative evaluation of the paper across key criteria important for audio plugin development. Each criterion is scored, and a total weighted score indicates its overall potential.",
  'detailed_pillar_analysis': "In-depth discussion and analysis of the paper's performance against each of the main evaluation criteria (pillars). This section elaborates on the reasoning behind the scores.",
  'multi_level_explanation': "The core idea of the paper explained at four different levels of complexity, making it understandable whether you're a musician, a JUCE developer, a music tech researcher, or an AI specialist.",
  'multi-level_explanations': "The core idea of the paper explained at four different levels of complexity, making it understandable whether you're a musician, a JUCE developer, a music tech researcher, or an AI specialist.",
  'brainstormed_audio_applications': "Creative ideas and potential starting points for how the paper's concepts could be used to build new audio plugins or features, with notes on JUCE integration.",
  'key_learnings_for_audio_dev': "The most important takeaways and actionable insights from the paper specifically for someone developing audio plugins with JUCE/C++.",
  'key_learnings_for_audio_developers': "The most important takeaways and actionable insights from the paper specifically for someone developing audio plugins with JUCE/C++.",
  'critical_assessment_and_limitations': "A balanced look at the paper's strengths and weaknesses from an audio developer's viewpoint, including potential issues for real-world plugin application.",
  'juce_implementation_sketch': "A conceptual outline of how one might start building a JUCE-based audio plugin inspired by the paper, including potential challenges and estimated effort.",
  'methodological_deep_dive_adaptation': "A detailed breakdown of the paper's core AI methods, simplified for audio developers, with prerequisites, a conceptual guide for adapting it to audio, and a practical audio example.",
  'methodological_deep_dive_&_adaptation': "A detailed breakdown of the paper's core AI methods, simplified for audio developers, with prerequisites, a conceptual guide for adapting it to audio, and a practical audio example.",
  'impact_on_my_research_and_development': "A personal reflection on how this paper influences the researcher's own audio plugin development projects, thesis work, and future research directions.",
  'impact_on_my_research_&_development': "A personal reflection on how this paper influences the researcher's own audio plugin development projects, thesis work, and future research directions.",
  'final_verdict_for_audio_dev': "A concluding summary and recommendation: Is this paper worth a thorough read for an audio plugin developer? Includes a subjective suitability score and final thoughts.",
  'final_verdict_for_audio_developers': "A concluding summary and recommendation: Is this paper worth a thorough read for an audio plugin developer? Includes a subjective suitability score and final thoughts.",

  // Nested sections within scores and detailed_pillar_analysis
  'implementation_readiness': "Assesses how easy it is to actually use or build upon the paper's work. Covers code availability, build instructions, environment setup, and examples.",
  'verified_performance_impact': "Evaluates if the paper shows real, measurable improvements (like better speed, accuracy, or sound quality) backed by solid evidence.",
  'problem_solving_novelty_insight': "Focuses on the paper's originality. Does it offer a truly new way to solve a problem, fresh insights, or a new perspective relevant to audio innovation?",
  'audio_domain_translatability_impact': "Examines how well the paper's ideas could be applied or adapted to audio software development, especially for JUCE/C++ plugins, and its potential impact.",
  'total_weighted_score': "The overall score for the paper, calculated by weighting each of the four main pillars based on their importance for practical audio plugin development.",

  // Sub-criteria tooltips for Implementation Readiness
  'code_link_license': "Is there public code and a clear license to use it?",
  'build_snippet': "Are there clear instructions to compile and run the code?",
  'environment_spec': "Does it specify the necessary software/hardware (e.g., CUDA, compiler versions)?",
  'minimal_example': "Is there a small, working example that shows the core idea?",

  // Sub-criteria tooltips for Verified Performance Impact
  'metric_table': "Does it provide clear data (CPU, latency, etc.) showing its performance?",
  'benchmarked_code_output': "Is there proof of better results (e.g., improved audio, clearer code)?",
  'stat_sig_repetition': "Are the results shown to be consistent and statistically reliable?",

  // Sub-criteria tooltips for Problem Solving Novelty Insight
  'conceptual_innovation': "Does it present a genuinely new idea, algorithm, or approach?",
  'problem_re_framing': "Does it offer a new way of looking at an existing challenge?",
  'clarity_of_explanation': "Is the core new idea explained clearly and understandably?",
  'potential_for_unforeseen_applications': "Could this idea be useful in other (audio) areas, even if not mentioned?",

  // Sub-criteria tooltips for Audio Domain Translatability Impact
  'direct_audio_application': "Can this be directly used for an audio task (e.g., a new effect)?",
  'conceptual_audio_analogy': "If not directly audio, can a strong comparison be made to an audio problem?",
  'juce_cpp_integration_pathway': "How feasible is it to implement this in a JUCE/C++ plugin (considering real-time needs)?",
  'workflow_enhancement_potential': "Could this improve how audio plugins are made or used (e.g., AI-assisted sound design)?",

  // Multi-level explanation subsections
  'for_a_musician_friend_(level_1)': "The paper's main idea explained in super simple terms, for someone who loves music but isn't a tech expert. Focuses on what it means for sound or music making.",
  'for_a_juce_developer_(no_ai_expertise)_(level_2)': "Explains the paper for a JUCE/C++ coder who knows audio but isn't deep into AI. Covers how it works conceptually and how it might fit into a plugin.",
  'for_a_music_technology_researcher_(level_3)': "A more technical dive for those in music technology research. Uses some AI terms (explained) and discusses its novelty for audio AI.",
  'for_an_ai_specialist_(condensed)_(level_4)': "A concise summary for AI experts. Uses specific AI jargon and highlights what's new or interesting from an AI research standpoint.",

  // Default fallback
  'default': "Information about this section of the paper evaluation."
};

// Component for pillar title with gradient-colored information icon
interface PillarTitleWithGradientIconProps {
  pillarKey: string;
  pillarIconRef?: (el: SVGSVGElement | null) => void;
  infoIconRef?: (el: SVGSVGElement | null) => void;
  pillarIconColor?: string;
  infoIconColor?: string;
}

const PillarTitleWithGradientIcon: React.FC<PillarTitleWithGradientIconProps> = ({ pillarKey, pillarIconRef, infoIconRef, pillarIconColor, infoIconColor }) => {
  const PillarIcon = getNarrativePillarIcon(pillarKey);

  return (
    <div className="flex justify-between items-center mb-2">
      <h4 className="text-md font-semibold text-gray-800 dark:text-gray-100 flex items-center">
        <PillarIcon ref={pillarIconRef} className="h-5 w-5 mr-2 shrink-0" style={pillarIconColor ? { color: pillarIconColor } : {}} />
        <InlineGlossaryText text={formatScoreKey(pillarKey)} />
      </h4>
      <Tooltip text={getSectionTooltip(pillarKey)}>
        <InformationCircleIcon ref={infoIconRef} className="h-5 w-5 shrink-0" style={infoIconColor ? { color: infoIconColor } : {}} />
      </Tooltip>
    </div>
  );
};

// Helper function to get tooltip text for a section
const getSectionTooltip = (sectionTitle: string): string => {
  // Normalize the title to match our keys
  const normalizedTitle = sectionTitle.toLowerCase()
    .replace(/\s+/g, '_')
    .replace(/[^\w\s_()-]/g, '')
    .replace(/_+/g, '_')
    .trim();

  // Try exact match first
  if (sectionTooltips[normalizedTitle]) {
    return sectionTooltips[normalizedTitle];
  }

  // Try some common variations
  const variations = [
    normalizedTitle.replace(/_/g, ' '),
    normalizedTitle.replace(/_/g, '-'),
    normalizedTitle.replace(/s$/, ''), // Remove trailing 's'
    normalizedTitle + 's', // Add trailing 's'
    normalizedTitle.replace(/&/g, 'and'), // Replace & with and
    normalizedTitle.replace(/and/g, '&'), // Replace and with &
  ];

  for (const variation of variations) {
    if (sectionTooltips[variation]) {
      return sectionTooltips[variation];
    }
  }

  // Debug: log when we can't find a tooltip
  console.log(`No tooltip found for section: "${sectionTitle}" (normalized: "${normalizedTitle}")`);

  // Return default if no match found
  return sectionTooltips['default'];
};

const PaperEvaluationV3: React.FC<{ evaluationData: EvaluationDataV3; showRawMarkdown: boolean }> = ({ evaluationData, showRawMarkdown }) => {
  if (showRawMarkdown) {
    return (
      <SectionCard
        title="Raw Markdown"
        id={titleToId("Raw Markdown")}
        leadingIcon={MainDocumentTextIcon}
        tooltipText="Raw JSON data structure of the paper evaluation, displayed for debugging and development purposes."
      >
        <pre className="whitespace-pre-wrap text-sm font-mono bg-gray-50 dark:bg-zinc-800 p-4 rounded-md overflow-x-auto">
          {JSON.stringify(evaluationData, null, 2)}
        </pre>
      </SectionCard>
    );
  }

  if (!evaluationData) {
    return <p>No evaluation data available.</p>;
  }

  const {
    metadata,
    executive_summary_for_audio_dev,
    scores,
    detailed_pillar_analysis,
    multi_level_explanation,
    brainstormed_audio_applications,
    key_learnings_for_audio_dev,
    critical_assessment_and_limitations,
    juce_implementation_sketch,
    methodological_deep_dive_adaptation,
    impact_on_my_research_and_development,
    final_verdict_for_audio_dev
  } = evaluationData;

  const quantitativeScoresContainerRef = useRef<HTMLDivElement>(null);
  const elementRefs = useRef<Record<string, Element | null>>({}); 
  const [elementColors, setElementColors] = useState<Record<string, string>>({});

  useEffect(() => {
    const calculateColors = () => {
      if (!quantitativeScoresContainerRef.current || Object.keys(elementRefs.current).length === 0) return;

      const containerRect = quantitativeScoresContainerRef.current.getBoundingClientRect();
      const newColors: Record<string, string> = {};

      Object.keys(elementRefs.current).forEach(key => {
        const itemElement = elementRefs.current[key];
        if (itemElement) {
          const itemRect = itemElement.getBoundingClientRect();
          const itemCenterXPx = (itemRect.left - containerRect.left) + (itemRect.width / 2);
          let percentage = itemCenterXPx / containerRect.width;
          percentage = Math.max(0, Math.min(1, percentage)); // Clamp between 0 and 1

          if (key.endsWith('-BarFill')) { 
            const barStartPx = itemRect.left - containerRect.left;
            const barEndPx = itemRect.right - containerRect.left;
            let startPercentage = barStartPx / containerRect.width;
            let endPercentage = barEndPx / containerRect.width;

            startPercentage = Math.max(0, Math.min(1, startPercentage));
            endPercentage = Math.max(0, Math.min(1, endPercentage));

            const color1 = interpolateColor(startPercentage, START_COLOR_RGB, END_COLOR_RGB);
            const color2 = interpolateColor(endPercentage, START_COLOR_RGB, END_COLOR_RGB);
            newColors[key] = `linear-gradient(to right, ${color1}, ${color2})`;
          } else {
            newColors[key] = interpolateColor(percentage, START_COLOR_RGB, END_COLOR_RGB);
          }
        }
      });
      setElementColors(newColors);
    };

    calculateColors(); 
    const debouncedCalculateColors = debounce(calculateColors, 100);
    window.addEventListener('resize', debouncedCalculateColors);

    return () => {
      window.removeEventListener('resize', debouncedCalculateColors);
    };
  }, [scores]); 

  function debounce<F extends (...args: any[]) => any>(func: F, waitFor: number) {
    let timeout: ReturnType<typeof setTimeout> | null = null;
    return (...args: Parameters<F>): Promise<ReturnType<F>> =>
      new Promise(resolve => {
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(() => resolve(func(...args)), waitFor);
      });
  }

  const multiLevelTooltips: Record<string, string> = {
    'level_1_musician_friend': "The paper's main idea explained in super simple terms, for someone who loves music but isn't a tech expert. Focuses on what it means for sound or music making.",
    'level_2_juce_developer_no_ai_expert': "Explains the paper for a JUCE/C++ coder who knows audio but isn't deep into AI. Covers how it works conceptually and how it might fit into a plugin.",
    'level_3_music_tech_researcher': "A more technical dive for those in music technology research. Uses some AI terms (explained) and discusses its novelty for audio AI.",
    'level_4_ai_specialist_condensed': "A concise summary for AI experts. Uses specific AI jargon and highlights what's new or interesting from an AI research standpoint."
  };

  return (
    <div className="pb-8 font-roboto dark:bg-zinc-900">
      {executive_summary_for_audio_dev && (
        <SectionCard
          title="Executive Summary for Audio Developers"
          id={titleToId("Executive Summary for Audio Developers")}
          leadingIcon={MainSparklesIcon}
          tooltipText={getSectionTooltip("Executive Summary for Audio Developers")}
        >
          <div className="pl-0">
            <MarkdownContent content={executive_summary_for_audio_dev} />
          </div>
        </SectionCard>
      )}

      {scores && (scores.total_weighted_score !== undefined || Object.values(scores).some(val => typeof val === 'object' && Object.keys(val).length > 0)) && (
        <SectionCard
          title="Quantitative Scores"
          id={titleToId("Quantitative Scores")}
          leadingIcon={ScaleIcon} // Changed from MainScaleIcon to outline ScaleIcon
          tooltipText={getSectionTooltip("Quantitative Scores")}
        >
          <div ref={quantitativeScoresContainerRef} className="relative">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-6">
              {Object.entries(scores).map(([pillarKey, pillarValue]) => {
                if (pillarKey === 'total_weighted_score' || typeof pillarValue !== 'object') return null;
                const pillarScoresObj = pillarValue as { [key: string]: any };
                if (Object.keys(pillarScoresObj).length === 0) return null;

                const { total: pillarTotalScore, ...subCriteria } = pillarScoresObj;
                const pillarIconId = `pillar-${pillarKey}-PillarIcon`;
                const pillarInfoIconId = `pillar-${pillarKey}-InfoIcon`;

                return (
                  <div key={pillarKey} className="p-4 bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col h-full">
                    <div className="flex-grow">
                      <PillarTitleWithGradientIcon
                        pillarKey={pillarKey}
                        pillarIconRef={(el) => { elementRefs.current[pillarIconId] = el; }}
                        infoIconRef={(el) => { elementRefs.current[pillarInfoIconId] = el; }}
                        pillarIconColor={elementColors[pillarIconId]}
                        infoIconColor={elementColors[pillarInfoIconId]}
                      />
                      <div className="space-y-2">
                        {Object.entries(subCriteria).map(([scoreName, scoreValue]) => {
                          const numericScore = typeof scoreValue === 'number' ? scoreValue : 0;
                          const IconComponent = getSubCriterionIcon(scoreName);
                          const subIconId = `sub-${pillarKey}-${scoreName}-Icon`;
                          const subInfoIconId = `sub-${pillarKey}-${scoreName}-InfoIcon`;
                          const subBarFillId = `sub-${pillarKey}-${scoreName}-BarFill`;

                          return (
                            <div key={scoreName} className="text-sm">
                              <div className={`flex justify-between items-center`}>
                                <div className="flex items-center">
                                  <IconComponent
                                    ref={(el: SVGSVGElement | null) => { elementRefs.current[subIconId] = el; }}
                                    className="h-4 w-4 mr-1.5 shrink-0"
                                    style={{ color: elementColors[subIconId] }}
                                  />
                                  <span className={`text-gray-600 dark:text-gray-300 flex-grow`}>
                                    <InlineGlossaryText text={formatScoreKey(scoreName)} />:
                                  </span>
                                  <Tooltip text={getSectionTooltip(scoreName)}>
                                    <InformationCircleIcon
                                      ref={(el: SVGSVGElement | null) => { elementRefs.current[subInfoIconId] = el; }}
                                      className="h-4 w-4 ml-1 shrink-0"
                                      style={{ color: elementColors[subInfoIconId] }}
                                    />
                                  </Tooltip>
                                </div>
                                <span className={`font-semibold text-gray-800 dark:text-gray-100 ml-2`}>{numericScore}/100</span>
                              </div>
                              <div className="mt-1 h-2 w-full bg-gray-200 dark:bg-zinc-800 rounded-full overflow-hidden relative">
                                {scoreMarkers.map(markerPos => (
                                  <div
                                    key={markerPos}
                                    className="absolute top-0 bottom-0 w-px bg-gray-300 dark:bg-zinc-700"
                                    style={{ left: `${markerPos}%` }}
                                  ></div>
                                ))}
                                <div
                                  ref={(el: HTMLDivElement | null) => { elementRefs.current[subBarFillId] = el; }}
                                  className="h-full rounded-full"
                                  style={{
                                    width: `${numericScore}%`, 
                                    background: elementColors[subBarFillId] 
                                  }}
                                ></div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                    {pillarTotalScore !== undefined && typeof pillarTotalScore === 'number' && (
                      <div className="mt-auto pt-3">
                        <div className="flex justify-between items-center text-sm mb-1">
                          <span className="font-bold text-gray-800 dark:text-gray-100">Total:</span>
                          <span className="font-extrabold text-gray-900 dark:text-gray-50">{pillarTotalScore}/100</span>
                        </div>
                        <div className="h-2.5 w-full bg-gray-200 dark:bg-zinc-800 rounded-full overflow-hidden">
                          <div
                            ref={(el: HTMLDivElement | null) => { elementRefs.current[`pillar-${pillarKey}-TotalBarFill`] = el; }}
                            className="h-full rounded-full"
                            style={{
                              width: `${pillarTotalScore}%`,
                              background: elementColors[`pillar-${pillarKey}-TotalBarFill`] 
                            }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {typeof scores.total_weighted_score === 'number' && (
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center mb-1">
                  <h4 
                    className="text-lg font-bold text-gray-900 dark:text-gray-50 flex items-center" 
                  >
                    Total Weighted Score
                    <Tooltip text={getSectionTooltip('total_weighted_score')}> 
                      <InformationCircleIcon 
                        className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" 
                      />
                    </Tooltip>
                  </h4>
                  <span 
                    className="text-2xl font-extrabold text-gray-900 dark:text-gray-50" 
                  >
                    {scores.total_weighted_score.toFixed(2)}
                  </span>
                </div>
                <div className="h-3 w-full bg-gray-200 dark:bg-zinc-800 rounded-full overflow-hidden">
                  <div
                    className="h-full rounded-full"
                    style={{ 
                      width: `${(scores.total_weighted_score / 100) * 100}%`, 
                      background: `linear-gradient(to right, rgb(${START_COLOR_RGB.join(',')}), rgb(${END_COLOR_RGB.join(',')}))` 
                    }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </SectionCard>
      )}

      {detailed_pillar_analysis && Object.keys(detailed_pillar_analysis).length > 0 && (
        <div className="space-y-8">
          {Object.entries(detailed_pillar_analysis).map(([pillarKey, details]) => {
            const PillarIcon = getNarrativePillarIcon(pillarKey);
            const pillarTitle = formatScoreKey(pillarKey);

            return (
              <SectionCard
                key={pillarKey}
                title={pillarTitle}
                id={titleToId(pillarTitle)}
                leadingIcon={PillarIcon}
                tooltipText={getSectionTooltip(pillarTitle)}
              >
                <div className="space-y-4">
                  {typeof details === 'object' && details !== null && Object.entries(details as PillarNarratives).map(([subPillarKey, subDetails]) => {
                    const SubPillarIcon = getSubCriterionIcon(subPillarKey);

                    const isDirectNarrative = typeof subDetails === 'string';
                    const isArrayNarrative = Array.isArray(subDetails);
                    const isNestedNarrative = typeof subDetails === 'object' && subDetails !== null && !Array.isArray(subDetails);

                    if (!subDetails || (isArrayNarrative && (subDetails as string[]).length === 0)) {
                      return null;
                    }

                    return (
                      <div key={subPillarKey} className="mb-3">
                        <div className="flex items-center mb-1.5">
                          {SubPillarIcon && <SubPillarIcon className="h-5 w-5 mr-1.5 text-blue-600 dark:text-blue-400 shrink-0" />}
                          <h4 className="text-lg font-medium text-gray-700 dark:text-gray-200 capitalize flex items-center">
                            <InlineGlossaryText text={formatScoreKey(subPillarKey)} />
                            <Tooltip text={getSectionTooltip(subPillarKey)}>
                              <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                            </Tooltip>
                          </h4>
                        </div>
                        {isDirectNarrative && (
                          <div className="ml-7">
                            <MarkdownContent content={subDetails as string} />
                          </div>
                        )}
                        {isArrayNarrative && (subDetails as string[]).length > 0 && (
                          <ul className="list-disc list-outside ml-11 space-y-1">
                            {(subDetails as string[]).map((item, index) => (
                              item && <li key={index} className="text-gray-700 dark:text-gray-300"><MarkdownContent content={item} /></li>
                            ))}
                          </ul>
                        )}
                        {isNestedNarrative && (
                          <div className="ml-7 space-y-2">
                            {Object.entries(subDetails as Record<string, string>).map(([itemKey, itemValue]) => (
                              itemValue && <div key={itemKey}>
                                <h5 className="text-md font-semibold text-gray-600 dark:text-gray-300 capitalize">{formatScoreKey(itemKey)}:</h5>
                                <MarkdownContent content={itemValue} />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </SectionCard>
            );
          })}
        </div>
      )}

      {multi_level_explanation && Object.values(multi_level_explanation).some(val => !!val) && (
        <SectionCard
          title="Multi-Level Explanations"
          id={titleToId("Multi-Level Explanations")}
          leadingIcon={MainAcademicCapIcon}
          className="mt-8"
          tooltipText={getSectionTooltip("Multi-Level Explanations")}
        >
          <div className="space-y-6">
            {Object.entries(multi_level_explanation).map(([levelKey, explanation]) => (
              explanation && <div key={levelKey} className="">
                <div className="flex items-center mb-2">
                  {levelKey === 'level_1_musician_friend' && <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />}
                  {levelKey === 'level_2_juce_developer_no_ai_expert' && <CodeBracketIcon className="h-6 w-6 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />}
                  {levelKey === 'level_3_music_tech_researcher' && <BeakerIcon className="h-6 w-6 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />}
                  {levelKey === 'level_4_ai_specialist_condensed' && <CogIcon className="h-6 w-6 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />}
                  
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                    <InlineGlossaryText text={formatMultiLevelKey(levelKey)} />
                  </h3>
                  {multiLevelTooltips[levelKey] && (
                    <Tooltip text={multiLevelTooltips[levelKey]!}>
                      <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                    </Tooltip>
                  )}
                </div>
                <div>
                  <MarkdownContent content={typeof explanation === 'string' ? explanation : ''} />
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {brainstormed_audio_applications && brainstormed_audio_applications.length > 0 && brainstormed_audio_applications.some(app => app.application_idea) && (
        <SectionCard
          title="Brainstormed Audio Applications"
          id={titleToId("Brainstormed Audio Applications")}
          leadingIcon={LightBulbIcon} // Changed from SubLightBulbIcon
          tooltipText={getSectionTooltip("Brainstormed Audio Applications")}
        >
          <div className="space-y-6">
            {brainstormed_audio_applications.map((app, index) => (
              app.application_idea && <div key={index} className="p-4 bg-gray-50 dark:bg-zinc-700/70 rounded-md shadow-sm">
                <h3 className="text-xl font-semibold mb-2 pb-2 border-b border-blue-200 dark:border-blue-600 text-blue-600 dark:text-blue-300">
                  <InlineGlossaryText text={app.application_idea} />
                </h3>
                <div className="pl-6">
                  <MarkdownContent content={typeof app.brief_description === 'string' ? app.brief_description : ''} />
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {key_learnings_for_audio_dev && key_learnings_for_audio_dev.length > 0 && key_learnings_for_audio_dev.some(l => !!l) && (
        <SectionCard
          title="Key Learnings for Audio Developers"
          id={titleToId("Key Learnings for Audio Developers")}
          leadingIcon={MainKeyIcon}
          tooltipText={getSectionTooltip("Key Learnings for Audio Developers")}
        >
          <ul className="list-disc list-outside pr-2 py-2 space-y-1">
            {key_learnings_for_audio_dev.map((learning, index) => (
              learning && <li key={index} className="text-gray-700 dark:text-gray-300 ml-4"> 
                <MarkdownContent content={typeof learning === 'string' ? learning : ''} />
              </li>
            ))}
          </ul>
        </SectionCard>
      )}

      {critical_assessment_and_limitations && (
        <SectionCard
          title="Critical Assessment and Limitations"
          id={titleToId("Critical Assessment and Limitations")}
          leadingIcon={MainExclamationTriangleIcon}
          tooltipText={getSectionTooltip("Critical Assessment and Limitations")}
        >
          <div className="pl-0">
            <MarkdownContent content={critical_assessment_and_limitations} />
          </div>
        </SectionCard>
      )}

      {juce_implementation_sketch && (juce_implementation_sketch.description || juce_implementation_sketch.code_sketch) && (
        <SectionCard
          title="JUCE Implementation Sketch"
          id={titleToId("JUCE Implementation Sketch")}
          leadingIcon={MainCodeBracketSquareIcon}
          tooltipText={getSectionTooltip("JUCE Implementation Sketch")}
        >
          {juce_implementation_sketch.description && (
            <div className="mb-4">
              <h4 className="text-lg font-semibold text-purple-600 dark:text-purple-400 mb-2">Conceptual Outline:</h4>
              <div className="pl-6">
                <MarkdownContent content={juce_implementation_sketch.description} />
              </div>
            </div>
          )}
          {juce_implementation_sketch.code_sketch && (
            <div>
              <h4 className="text-lg font-semibold text-purple-600 dark:text-purple-400 mb-2">Code Sketch (Pseudocode/C++):</h4>
              {showRawMarkdown ? (
                <pre className="p-4 bg-gray-800 dark:bg-black text-white dark:text-gray-200 rounded-md shadow-inner overflow-x-auto text-sm font-mono whitespace-pre-wrap break-all">
                  <code>{juce_implementation_sketch.code_sketch}</code>
                </pre>
              ) : (
                <div className="pl-6">
                  <div className="p-4 bg-gray-800 dark:bg-black rounded-md shadow-inner overflow-x-auto">
                    <MarkdownContent content={`\`\`\`cpp\n${juce_implementation_sketch.code_sketch}\n\`\`\``} />
                  </div>
                </div>
              )}
            </div>
          )}
        </SectionCard>
      )}

      {methodological_deep_dive_adaptation && methodological_deep_dive_adaptation.length > 0 && methodological_deep_dive_adaptation.some(item => item.methodName) && (
        <SectionCard 
          title="Methodological Deep Dive & Adaptation" 
          id={titleToId("Methodological Deep Dive & Adaptation")}
          leadingIcon={MainCubeTransparentIcon} // Or CubeTransparentIcon from outline if preferred
          tooltipText={getSectionTooltip("Methodological Deep Dive & Adaptation for Audio")}
        >
          <div className="space-y-8"> 
            {methodological_deep_dive_adaptation.map((item, index) => (
              item.methodName && <div key={index} className="py-4"> 
                <h3 className="text-xl font-semibold border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100">
                  <InlineGlossaryText text={typeof item.methodName === 'string' ? item.methodName : 'Unnamed Method'} />
                  <Tooltip text="The official name of the core AI technique or method discussed in the paper.">
                    <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400 inline-block" />
                  </Tooltip>
                </h3>
                <div className="space-y-4 mt-3 pl-0"> 
                  {item.simplifiedExplanationForAudioDev && (
                    <div>
                      <div className="flex items-center mb-1">
                        <ChatBubbleBottomCenterTextIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" /> {/* Original icon was LightBulbIcon, but ChatBubbleBottomCenterTextIcon is in JSON for this section */}
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Simplified Explanation for Audio Devs:</h4>
                        <Tooltip text="The method explained simply, with a focus on how it might be relevant or useful for audio tasks.">
                          <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                        </Tooltip>
                      </div>
                      <div className="pl-7"> 
                        <MarkdownContent content={typeof item.simplifiedExplanationForAudioDev === 'string' ? item.simplifiedExplanationForAudioDev : ''} />
                      </div>
                    </div>
                  )}
                  {item.prerequisites_for_audio_adaptation && item.prerequisites_for_audio_adaptation.length > 0 && item.prerequisites_for_audio_adaptation.some(p => !!p) && (
                    <div>
                      <div className="flex items-center mb-1">
                        <ClipboardDocumentListIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Prerequisites for Audio Adaptation:</h4>
                        <Tooltip text="What skills, tools, or data would be needed to try and use this method for an audio application.">
                          <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                        </Tooltip>
                      </div>
                      <ul className="list-disc list-outside pl-7 pr-2 py-2 space-y-1"> 
                        {item.prerequisites_for_audio_adaptation.map((prerequisite, pIndex) => (
                          prerequisite && <li key={pIndex} className="text-gray-700 dark:text-gray-300 ml-0"> 
                            <MarkdownContent content={typeof prerequisite === 'string' ? prerequisite : ''} />
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {item.stepByStepAdaptationGuide_conceptual && item.stepByStepAdaptationGuide_conceptual.length > 0 && item.stepByStepAdaptationGuide_conceptual.some(s => !!s) && (
                    <div>
                      <div className="flex items-center mb-1">
                        <ListBulletIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Step-by-Step Conceptual Adaptation Guide:</h4>
                        <Tooltip text="A thinking guide: steps to consider if you wanted to adapt this method for use in audio software.">
                          <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                        </Tooltip>
                      </div>
                      <ol className="list-decimal list-outside pl-7 pr-2 py-2 space-y-1"> {/* Changed to ol for steps */}
                        {item.stepByStepAdaptationGuide_conceptual.map((step, sIndex) => (
                          step && <li key={sIndex} className="text-gray-700 dark:text-gray-300 ml-0"> 
                            <MarkdownContent content={typeof step === 'string' ? step : ''} />
                          </li>
                        ))}
                      </ol>
                    </div>
                  )}
                  {item.practicalAudioExample && (item.practicalAudioExample.scenarioDescription || item.practicalAudioExample.how_method_might_apply || item.practicalAudioExample.expected_audio_outcome_if_successful) && (
                    <div>
                      <div className="flex items-center mb-1">
                        <BeakerIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Practical Audio Example:</h4>
                        <Tooltip text="The consistent audio development challenge used for comparison across papers.">
                          <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                        </Tooltip>
                      </div>
                      <div className="pl-7"> 
                        {item.practicalAudioExample.scenarioDescription && (
                          <div className="mb-3">
                            <p className="italic text-gray-600 dark:text-gray-400">
                              Scenario: 
                              <InlineGlossaryText text={typeof item.practicalAudioExample.scenarioDescription === 'string' ? item.practicalAudioExample.scenarioDescription : ''} />
                            </p>
                          </div>
                        )}
                        {item.practicalAudioExample.how_method_might_apply && (
                          <div className="mb-1">
                            <div className="flex items-center">
                              <strong className="text-gray-700 dark:text-gray-200">Application:</strong> 
                              <Tooltip text="How this paper's technique could specifically address the standard audio scenario.">
                                <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                              </Tooltip>
                            </div>
                            <MarkdownContent content={typeof item.practicalAudioExample.how_method_might_apply === 'string' ? item.practicalAudioExample.how_method_might_apply : ''} />
                          </div>
                        )}
                        {item.practicalAudioExample.expected_audio_outcome_if_successful && (
                          <div>
                            <div className="flex items-center">
                              <strong className="text-gray-700 dark:text-gray-200">Expected Outcome:</strong> 
                              <Tooltip text="What the ideal result would be if this method worked well in the audio scenario.">
                                <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                              </Tooltip>
                            </div>
                            <MarkdownContent content={typeof item.practicalAudioExample.expected_audio_outcome_if_successful === 'string' ? item.practicalAudioExample.expected_audio_outcome_if_successful : ''} />
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {impact_on_my_research_and_development && Object.values(impact_on_my_research_and_development).some(val => !!val || (Array.isArray(val) && val.length > 0 && val.some(q => !!q))) && (
        <SectionCard
          title="Impact on My Research & Development"
          id={titleToId("Impact on My Research & Development")}
          leadingIcon={BookOpenIcon} // Changed from SubBookOpenIcon
          tooltipText={getSectionTooltip("Impact on My Research & Development")}
        >
          <div className="space-y-6">
            {impact_on_my_research_and_development.new_ideas_sparked && (
              <div>
                <div className="flex items-center mb-1">
                  <LightBulbIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" /> {/* Changed from SubLightBulbIcon */}
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">New Ideas Sparked:</h4>
                  <Tooltip text="Any brand-new plugin or research ideas that came to the evaluator after reading this paper.">
                    <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                  </Tooltip>
                </div>
                <div className="pl-7">
                  <MarkdownContent content={typeof impact_on_my_research_and_development.new_ideas_sparked === 'string' ? impact_on_my_research_and_development.new_ideas_sparked : ''} />
                </div>
              </div>
            )}
            {impact_on_my_research_and_development.refinement_of_existing_ideas && (
              <div>
                <div className="flex items-center mb-1">
                  <PencilSquareIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Refinement of Existing Ideas:</h4>
                  <Tooltip text="How this paper might help improve or change ideas the evaluator was already working on.">
                    <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                  </Tooltip>
                </div>
                <div className="pl-7">
                  <MarkdownContent content={typeof impact_on_my_research_and_development.refinement_of_existing_ideas === 'string' ? impact_on_my_research_and_development.refinement_of_existing_ideas : ''} />
                </div>
              </div>
            )}
            {impact_on_my_research_and_development.potential_thesis_contribution_angle && (
              <div>
                <div className="flex items-center mb-1">
                  <AcademicCapIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" />
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Potential Thesis Contribution Angle:</h4>
                  <Tooltip text="How insights from this paper could specifically help with the evaluator's main research project or thesis.">
                    <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                  </Tooltip>
                </div>
                <div className="pl-7">
                  <MarkdownContent content={typeof impact_on_my_research_and_development.potential_thesis_contribution_angle === 'string' ? impact_on_my_research_and_development.potential_thesis_contribution_angle : ''} />
                </div>
              </div>
            )}
            {impact_on_my_research_and_development.questions_for_further_investigation && impact_on_my_research_and_development.questions_for_further_investigation.length > 0 && impact_on_my_research_and_development.questions_for_further_investigation.some(q => !!q) && (
              <div>
                <div className="flex items-center mb-1">
                  <QuestionMarkCircleIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" /> {/* Changed from SubQuestionMarkCircleIcon */}
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Questions for Further Investigation:</h4>
                  <Tooltip text="New questions or areas for future exploration that this paper brought up, especially for audio.">
                    <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                  </Tooltip>
                </div>
                <ul className="list-disc list-outside pl-7 pr-2 py-2 space-y-1">
                  {impact_on_my_research_and_development.questions_for_further_investigation.map((question, qIndex) => (
                    question && <li key={qIndex} className="text-gray-700 dark:text-gray-300">
                      <MarkdownContent content={typeof question === 'string' ? question : ''} />
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </SectionCard>
      )}

      {final_verdict_for_audio_dev && (
        final_verdict_for_audio_dev.is_worth_reading_thoroughly ||
        final_verdict_for_audio_dev.primary_value_proposition ||
        final_verdict_for_audio_dev.overall_suitability_score_for_my_needs ||
        final_verdict_for_audio_dev.concluding_remarks
      ) && (
        <SectionCard
          title="Final Verdict for Audio Developers"
          id={titleToId("Final Verdict for Audio Developers")}
          leadingIcon={MainCheckCircleIcon}
          tooltipText={getSectionTooltip("Final Verdict for Audio Developers")}
        >
          {final_verdict_for_audio_dev.is_worth_reading_thoroughly && (
            <div className="mb-4">
              <div className="flex items-center mb-1">
                <BookOpenIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" /> {/* Changed from SubBookOpenIcon */}
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Is it Worth Reading Thoroughly?</h4>
                <Tooltip text="A quick recommendation: Should an audio plugin developer read this whole paper, just parts, or skip it?">
                  <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                </Tooltip>
              </div>
              <div className="pl-7">
                <MarkdownContent content={typeof final_verdict_for_audio_dev.is_worth_reading_thoroughly === 'string' ? final_verdict_for_audio_dev.is_worth_reading_thoroughly : ''} />
              </div>
            </div>
          )}
          {final_verdict_for_audio_dev.primary_value_proposition && (
            <div className="mb-4">
              <div className="flex items-center mb-1">
                <LightBulbIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" /> {/* Changed from SubLightBulbIcon */}
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Primary Value Proposition:</h4>
                <Tooltip text="The single biggest reason (or lack thereof) for an audio developer to check out this paper.">
                  <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                </Tooltip>
              </div>
              <div className="pl-7">
                <MarkdownContent content={typeof final_verdict_for_audio_dev.primary_value_proposition === 'string' ? final_verdict_for_audio_dev.primary_value_proposition : ''} />
              </div>
            </div>
          )}
          {final_verdict_for_audio_dev.overall_suitability_score_for_my_needs && (
            <div className="mb-4">
              <div className="flex items-center mb-1">
                <ScaleIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" /> {/* Changed from SubScaleIcon */}
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Overall Suitability Score for My Needs:</h4>
                <Tooltip text="The evaluator's personal score (0-100) on how useful this paper is for their specific research goals, separate from the main weighted score.">
                  <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                </Tooltip>
              </div>
              <div className="pl-7">
                <MarkdownContent content={typeof final_verdict_for_audio_dev.overall_suitability_score_for_my_needs === 'string' ? final_verdict_for_audio_dev.overall_suitability_score_for_my_needs : ''} />
              </div>
            </div>
          )}
          {final_verdict_for_audio_dev.concluding_remarks && (
            <div className="mb-4"> 
              <div className="flex items-center mb-1">
                <DocumentTextIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 shrink-0" /> {/* Changed from SubDocumentTextIcon */}
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Concluding Remarks:</h4>
                <Tooltip text="The evaluator's final thoughts on the paper's value, its potential, and any last recommendations for fellow audio developers.">
                  <InformationCircleIcon className="h-5 w-5 ml-1.5 shrink-0 text-blue-600 dark:text-blue-400" />
                </Tooltip>
              </div>
              <div className="pl-7">
                <MarkdownContent content={typeof final_verdict_for_audio_dev.concluding_remarks === 'string' ? final_verdict_for_audio_dev.concluding_remarks : ''} />
              </div>
            </div>
          )}
        </SectionCard>
      )}
    </div>
  );
};

export default PaperEvaluationV3;
