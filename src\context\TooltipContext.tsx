'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';

interface TooltipContextType {
  activeTooltipId: string | null;
  setActiveTooltip: (id: string | null) => void;
  getTooltipZIndex: (id: string) => number;
}

const TooltipContext = createContext<TooltipContextType | undefined>(undefined);

export function TooltipProvider({ children }: { children: React.ReactNode }) {
  const [activeTooltipId, setActiveTooltipId] = useState<string | null>(null);

  const setActiveTooltip = useCallback((id: string | null) => {
    setActiveTooltipId(id);
  }, []);

  const getTooltipZIndex = useCallback((id: string) => {
    // Base z-index for tooltips
    const baseZIndex = 9999;
    
    // If this tooltip is active, give it the highest z-index
    if (activeTooltipId === id) {
      return baseZIndex + 100;
    }
    
    // Otherwise, use the base z-index
    return baseZIndex;
  }, [activeTooltipId]);

  return (
    <TooltipContext.Provider value={{
      activeTooltipId,
      setActiveTooltip,
      getTooltipZIndex
    }}>
      {children}
    </TooltipContext.Provider>
  );
}

export function useTooltip() {
  const context = useContext(TooltipContext);
  if (context === undefined) {
    throw new Error('useTooltip must be used within a TooltipProvider');
  }
  return context;
}
